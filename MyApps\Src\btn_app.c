#include "btn_app.h"

// 回调函数：处理按键事件
/**
 * EBTN_EVT_ONPRESS     当按键按下时,将事件发送给应用程序
 * EBTN_EVT_ONRELEASE   当按键抬起时,将事件发送给应用程序
 * EBTN_EVT_KEEPALIVE   当按键长按时,将事件定期发送给应用程序
 * EBTN_EVT_ONCLICK     仅当按键完成单击动作时,将事件发送给应用程序
 */
void BtnEventCallback(struct ebtn_btn *btn, ebtn_evt_t evt)
{
    switch (btn -> key_id)
    {
        // 单个按键事件
        case USER_BUTTON_1:
            switch (evt)
            {
                case EBTN_EVT_ONCLICK:
                    ucLed[0] = ucLed[0] ? 0 : 1;
                    break;
            }
            break;

        case USER_BUTTON_2:
            switch (evt)
            {
                case EBTN_EVT_ONCLICK:
                    ucLed[1] = ucLed[1] ? 0 : 1;
                break;
            }
            break;

         case USER_BUTTON_3:
            switch (evt)
            {
                case EBTN_EVT_ONCLICK:
                    ucLed[2] = ucLed[2] ? 0 : 1;
                break;
            }
            break;

         case USER_BUTTON_4:
            switch (evt)
            {
                case EBTN_EVT_ONCLICK:
                    ucLed[3] = ucLed[3] ? 0 : 1;
                break;
            }
            break;

         case USER_BUTTON_5:
            switch (evt)
            {
                case EBTN_EVT_ONCLICK:
                    ucLed[4] = ucLed[4] ? 0 : 1;
                break;
            }
            break;

         case USER_BUTTON_6:
            switch (evt)
            {
                case EBTN_EVT_ONCLICK:
                    ucLed[5] = ucLed[5] ? 0 : 1;
                break;
            }
            break;

        // 组合按键事件
        case USER_BUTTON_COMBO_1:
            switch (evt)
            {
                case EBTN_EVT_ONCLICK:
                    
                    break;

            }
            break;
    }
}

void BtnTask(void)
{
    ebtn_process(uwTick);
}
