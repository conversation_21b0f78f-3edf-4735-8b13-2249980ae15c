/*!
    \file    gd32f4xx_it.c
    \brief   interrupt service routines

    \version 2024-12-20, V3.3.1, firmware for GD32F4xx
*/

/*
    Copyright (c) 2024, GigaDevice Semiconductor Inc.

    Redistribution and use in source and binary forms, with or without modification,
are permitted provided that the following conditions are met:

    1. Redistributions of source code must retain the above copyright notice, this
       list of conditions and the following disclaimer.
    2. Redistributions in binary form must reproduce the above copyright notice,
       this list of conditions and the following disclaimer in the documentation
       and/or other materials provided with the distribution.
    3. Neither the name of the copyright holder nor the names of its contributors
       may be used to endorse or promote products derived from this software without
       specific prior written permission.

    THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.
IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,
INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT
NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR
PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY
OF SUCH DAMAGE.
*/

#include "gd32f4xx_it.h"

/*!
    \brief      this function handles NMI exception
    \param[in]  none
    \param[out] none
    \retval     none
*/
void NMI_Handler(void)
{
    /* if NMI exception occurs, go to infinite loop */
    while(1) {
    }
}

/*!
    \brief      this function handles HardFault exception
    \param[in]  none
    \param[out] none
    \retval     none
*/
void HardFault_Handler(void)
{
    /* if Hard Fault exception occurs, go to infinite loop */
    while(1) {
    }
}

/*!
    \brief      this function handles MemManage exception
    \param[in]  none
    \param[out] none
    \retval     none
*/
void MemManage_Handler(void)
{
    /* if Memory Manage exception occurs, go to infinite loop */
    while(1) {
    }
}

/*!
    \brief      this function handles BusFault exception
    \param[in]  none
    \param[out] none
    \retval     none
*/
void BusFault_Handler(void)
{
    /* if Bus Fault exception occurs, go to infinite loop */
    while(1) {
    }
}

/*!
    \brief      this function handles UsageFault exception
    \param[in]  none
    \param[out] none
    \retval     none
*/
void UsageFault_Handler(void)
{
    /* if Usage Fault exception occurs, go to infinite loop */
    while(1) {
    }
}

/*!
    \brief      this function handles SVC exception
    \param[in]  none
    \param[out] none
    \retval     none
*/
void SVC_Handler(void)
{
    /* if SVC exception occurs, go to infinite loop */
    while(1) {
    }
}

/*!
    \brief      this function handles DebugMon exception
    \param[in]  none
    \param[out] none
    \retval     none
*/
void DebugMon_Handler(void)
{
    /* if DebugMon exception occurs, go to infinite loop */
    while(1) {
    }
}

/*!
    \brief      this function handles PendSV exception
    \param[in]  none
    \param[out] none
    \retval     none
*/
void PendSV_Handler(void)
{
    /* if PendSV exception occurs, go to infinite loop */
    while(1) {
    }
}

/*!
    \brief    this function handles SysTick exception
    \param[in]  none
    \param[out] none
    \retval     none
*/
void SysTick_Handler(void)
{
    uwTick++;
    delay_decrement();
}

void USART0_IRQHandler(void)
{
    if(RESET != usart_interrupt_flag_get(USART0, USART_INT_FLAG_IDLE)){
        usart_interrupt_disable(USART0, USART_INT_IDLE);
        usart_data_receive(USART0);

        uint16_t received_len = USART0_BUFFER_SIZE - dma_transfer_number_get(DMA1, DMA_CH2);

        if(received_len > 0 && received_len < USART0_BUFFER_SIZE)
        {
            memcpy(usart0_rx_buffer_proc, usart0_rx_buffer_dma, received_len);
            usart0_rx_buffer_proc[received_len] = '\0';
            memset(usart0_rx_buffer_dma, 0, USART0_BUFFER_SIZE);
            usart0_rx_flag = 1;
        }

        dma_channel_disable(DMA1, DMA_CH2);
        dma_flag_clear(DMA1, DMA_CH2, DMA_FLAG_FTF);
        dma_transfer_number_config(DMA1, DMA_CH2, USART0_BUFFER_SIZE);
        dma_channel_enable(DMA1, DMA_CH2);

        usart_interrupt_enable(USART0, USART_INT_IDLE);
    }
}

void USART1_IRQHandler(void)
{
    if(RESET != usart_interrupt_flag_get(USART1, USART_INT_FLAG_IDLE)){
        usart_interrupt_disable(USART1, USART_INT_IDLE);
        usart_data_receive(USART1);
        
        uint16_t received_len = USART1_BUFFER_SIZE - dma_transfer_number_get(DMA0, DMA_CH5);
        if(received_len > 0 && received_len < USART1_BUFFER_SIZE)
        {
            memcpy(usart1_rx_buffer_proc, usart1_rx_buffer_dma, received_len);
            usart1_rx_buffer_proc[received_len] = '\0';
            memset(usart1_rx_buffer_dma, 0, USART1_BUFFER_SIZE);
            usart1_rx_flag = 1;
        }
        
        dma_channel_disable(DMA0, DMA_CH5);
        dma_flag_clear(DMA0, DMA_CH5, DMA_FLAG_FTF);
        dma_transfer_number_config(DMA0, DMA_CH5, USART1_BUFFER_SIZE);
        dma_channel_enable(DMA0, DMA_CH5);

        usart_interrupt_enable(USART0, USART_INT_IDLE);
    }
}

void SDIO_IRQHandler(void)
{
    sd_interrupts_process();
}
